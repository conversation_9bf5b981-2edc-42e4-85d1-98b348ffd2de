import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { HttpStatusCode } from 'axios';
import { firstValueFrom } from 'rxjs';
import { EStatusNode } from 'src/modules/workflow/enums/execution-status.enum';
import { NodeGroupEnum } from '../../../../../enums/node-group.enum';
import {
  EAuthType,
  EHttpMethod,
  IHttpRequestOutput,
  IHttpRequestParameters,
  validateHttpRequestParameters
} from '../../../../../interfaces/core/http/http-request.interface';
import { ENodeType } from '../../../../../interfaces/node-manager.interface';
import {
  ExecutorContext,
  ValidationResult
} from '../../base/node-executor.interface';
import { ValidationUtils } from '../../shared/validation.utils';

/**
 * Executor for HTTP_REQUEST node type
 * Handles HTTP requests with authentication, retry logic, and response processing
 */
@Injectable()
export class HttpRequestExecutor {
  readonly nodeGroup = NodeGroupEnum.HTTP;
  readonly supportedNodeTypes = [ENodeType.HTTP_REQUEST];
  readonly executorName = 'HttpRequestExecutor';
  readonly version = '1.0.0';
  private readonly logger = new Logger(HttpRequestExecutor.name);

  constructor(
    private readonly httpService: HttpService
  ) { }

  /**
   * Execute HTTP request with standardized input/output
   */
  public async execute(
    context: IHttpRequestParameters
  ): Promise<IHttpRequestOutput> {
    const startTime = Date.now();

    try {
      this.logger.debug(`Executing HTTP request: ${context.method} ${context.url}`);

      // Validate parameters
      const validation = validateHttpRequestParameters(context);
      if (!validation.isValid) {
        throw new Error(`Invalid HTTP request parameters: ${validation.errors.join(', ')}`);
      }

      // Build request configuration
      const config = this.buildRequestConfig(context);

      // Execute request based on method
      const response = await this.performRequest(context.method, context.url, context.body, config);

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      this.logger.debug(`HTTP request completed in ${executionTime}ms with status ${response.status}`);

      // Return result in IHttpRequestOutput format
      return {
        status: EStatusNode.SUCCESS,
        data: {
          code: response.status,
          response: response.data
        }
      };

    } catch (error) {
      const endTime = Date.now();
      const executionTime = endTime - startTime;

      this.logger.error(`HTTP request failed after ${executionTime}ms: ${error.message}`, error.stack);

      // Return error response in standardized format
      return {
        status: EStatusNode.ERROR,
        data: {
          code: HttpStatusCode.InternalServerError,
          response: {
            message: error.message || 'HTTP request failed',
          }
        }
      };
    }
  }

  /**
   * Build axios request configuration
   */
  private buildRequestConfig(parameters: IHttpRequestParameters): any {
    const config: any = {
      timeout: parameters.timeout || 30000,
      headers: parameters.headers ? { ...parameters.headers } : {},
      validateStatus: () => true // Don't throw on HTTP error status
    };

    // Add authentication
    if (parameters.auth_type && parameters.auth_type !== EAuthType.NONE) {
      switch (parameters.auth_type) {
        case EAuthType.BEARER:
          if (parameters.auth_config?.token) {
            config.headers['Authorization'] = `Bearer ${parameters.auth_config.token}`;
          }
          break;
        case EAuthType.BASIC:
          if (parameters.auth_config?.username && parameters.auth_config?.password) {
            config.auth = {
              username: parameters.auth_config.username,
              password: parameters.auth_config.password
            };
          }
          break;
        case EAuthType.API_KEY:
          if (parameters.auth_config?.api_key && parameters.auth_config?.api_key_header) {
            config.headers[parameters.auth_config.api_key_header] = parameters.auth_config.api_key;
          }
          break;
      }
    }

    // Add query parameters
    if (parameters.query_params) {
      config.params = { ...parameters.query_params };
    }

    // Set content type
    if (parameters.content_type) {
      config.headers['Content-Type'] = parameters.content_type;
    }

    return config;
  }

  /**
   * Perform HTTP request based on method
   */
  private async performRequest(
    method: EHttpMethod,
    url: string,
    body?: any,
    config?: any
  ): Promise<any> {
    switch (method) {
      case EHttpMethod.GET:
        return firstValueFrom(this.httpService.get(url, config));
      case EHttpMethod.POST:
        return firstValueFrom(this.httpService.post(url, body, config));
      case EHttpMethod.PUT:
        return firstValueFrom(this.httpService.put(url, body, config));
      case EHttpMethod.PATCH:
        return firstValueFrom(this.httpService.patch(url, body, config));
      case EHttpMethod.DELETE:
        return firstValueFrom(this.httpService.delete(url, config));
      case EHttpMethod.HEAD:
        return firstValueFrom(this.httpService.head(url, config));
      case EHttpMethod.OPTIONS:
        return firstValueFrom(this.httpService.request({ ...config, method: 'OPTIONS', url }));
      default:
        throw new Error(`Unsupported HTTP method: ${method}`);
    }
  }



  /**
   * Validate HTTP request node input
   */
  protected async validateNodeSpecificInput(
    context: ExecutorContext,
    result: ValidationResult
  ): Promise<void> {
    const params = context.node.parameters as IHttpRequestParameters;

    // Use existing validation function from interface
    const interfaceValidation = validateHttpRequestParameters(params);
    if (!interfaceValidation.isValid) {
      for (const error of interfaceValidation.errors) {
        ValidationUtils.addError(
          result,
          'INTERFACE_VALIDATION_ERROR',
          error,
          'parameters'
        );
      }
    }

    // Additional custom validations
    this.validateHttpRequestSpecific(params, result);
  }

  /**
   * HTTP request specific validations
   */
  private validateHttpRequestSpecific(
    params: IHttpRequestParameters,
    result: ValidationResult
  ): void {
    // Validate URL
    ValidationUtils.validateUrl(result, params.url, 'URL', 'url');

    // Validate method
    ValidationUtils.validateHttpMethod(result, params.method, 'HTTP method', 'method');

    // Validate timeout
    if (params.timeout) {
      ValidationUtils.validateTimeout(result, params.timeout, 'Timeout', 'timeout');
    }

    // Validate retry config
    if (params.retry_config) {
      const retry = params.retry_config;

      if (retry.max_retries !== undefined) {
        ValidationUtils.validateNumberRange(
          result,
          retry.max_retries,
          'Max retries',
          0,
          10,
          'retry_config.max_retries'
        );
      }

      if (retry.delay !== undefined) {
        ValidationUtils.validateNumberRange(
          result,
          retry.delay,
          'Retry delay',
          100,
          60000,
          'retry_config.delay'
        );
      }
    }

    // Validate authentication
    if (params.auth_config && params.auth_type) {
      this.validateAuthentication(params, result);
    }

    // Validate headers
    if (params.headers) {
      ValidationUtils.validateObject(
        result,
        params.headers,
        'Headers',
        undefined,
        'headers'
      );
    }

    // Validate body
    if (params.body) {
      if (typeof params.body !== 'string' && typeof params.body !== 'object') {
        ValidationUtils.addError(
          result,
          'INVALID_BODY_TYPE',
          'Body must be a string or object',
          'body',
          params.body
        );
      }
    }

    // Performance warnings
    if (params.timeout && params.timeout > 30000) {
      ValidationUtils.addWarning(
        result,
        'HIGH_TIMEOUT',
        'High timeout value may cause workflow delays',
        'timeout',
        'Consider using a lower timeout value'
      );
    }

    if (params.retry_config?.max_retries && params.retry_config.max_retries > 5) {
      ValidationUtils.addWarning(
        result,
        'HIGH_RETRY_COUNT',
        'High retry count may cause long execution times',
        'retry_config.max_retries',
        'Consider using fewer retries'
      );
    }
  }

  /**
   * Validate authentication configuration
   */
  private validateAuthentication(params: IHttpRequestParameters, result: ValidationResult): void {
    ValidationUtils.validateRequired(result, params.auth_type, 'Authentication type', 'auth_type');

    if (!params.auth_config) {
      ValidationUtils.addError(
        result,
        'MISSING_AUTH_CONFIG',
        'Authentication config is required when auth_type is specified',
        'auth_config'
      );
      return;
    }

    const auth = params.auth_config;

    switch (params.auth_type) {
      case EAuthType.BEARER:
        ValidationUtils.validateRequired(
          result,
          auth.token,
          'Bearer token',
          'auth_config.token'
        );
        break;

      case EAuthType.API_KEY:
        ValidationUtils.validateRequired(
          result,
          auth.api_key,
          'API key',
          'auth_config.api_key'
        );
        break;

      case EAuthType.BASIC:
        ValidationUtils.validateRequired(
          result,
          auth.username,
          'Username',
          'auth_config.username'
        );
        ValidationUtils.validateRequired(
          result,
          auth.password,
          'Password',
          'auth_config.password'
        );
        break;

      case EAuthType.OAUTH2:
        ValidationUtils.validateRequired(
          result,
          auth.token,
          'OAuth2 access token',
          'auth_config.token'
        );
        break;

      case EAuthType.NONE:
        // No validation needed for NONE
        break;

      default:
        ValidationUtils.addError(
          result,
          'INVALID_AUTH_TYPE',
          `Unsupported authentication type: ${params.auth_type}`,
          'auth_type',
          params.auth_type
        );
        break;
    }
  }
}
