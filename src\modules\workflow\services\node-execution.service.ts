import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { HttpStatusCode } from 'axios';
import { Node } from '../entities';
import { EStatusNode } from '../enums/execution-status.enum';
import { ENodeType, IHttpRequestOutput, IHttpRequestParameters } from '../interfaces';
import { WorkflowContext } from '../interfaces/workflow-context.interface';
import { NodeDefinitionRepository } from '../repositories/node-definition.repository';
import { NodeRepository } from '../repositories/node.repository';
import { NodeTypeUtils } from '../utils/node-type.utils';
import { WorkflowTemplateProcessorService } from './workflow-template-processor.service';
import { HttpRequestExecutor } from './xstate/executors/implementations/http/http-request.executor';

/**
 * Interface cho standalone node execution input
 */
export interface NodeExecutionInput {
  workflowId: string;
  nodeId: string;
  executionId: string;
  context: WorkflowContext;
  userId?: number;
}

/**
 * Service để thực thi standalone node execution
 * Cho phép execute một node cụ thể mà không cần chạy toàn bộ workflow
 */
@Injectable()
export class NodeExecutionService {
  private readonly logger = new Logger(NodeExecutionService.name);

  constructor(
    private readonly nodeRepository: NodeRepository,
    private readonly nodeDefinitionRepository: NodeDefinitionRepository,
    private readonly templateProcessor: WorkflowTemplateProcessorService,
    private readonly httpRequestExecutor: HttpRequestExecutor,
  ) { }

  /**
   * Execute một node cụ thể với context được cung cấp
   */
  async executeNode(input: NodeExecutionInput): Promise<Record<string, any>> {
    const startTime = Date.now();
    const { workflowId, nodeId, context } = input;

    this.logger.log(`Executing standalone node: ${nodeId} in workflow: ${workflowId}`);

    try {
      // 1. Load và validate node information
      const nodeInfo = await this.loadNodeInformation(workflowId, nodeId);

      // 3. Execute node logic
      const executionResult = await this.executeNodeLogic(
        nodeInfo.nodeType,
        nodeInfo.node,
        context,
      );

      const executionTime = Date.now() - startTime;

      // 4. Return formatted result
      return {
        success: true,
        nodeId,
        workflowId,
        executionTime,
        result: executionResult,
        metadata: {
          nodeType: nodeInfo.node.nodeDefinitionId || 'unknown',
          nodeName: nodeInfo.node.name || nodeId,
          executedAt: Date.now(),
          hasContext: Object.keys(context).length > 0,
          contextKeys: Object.keys(context),
        },
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;

      this.logger.error(`Standalone node execution failed: ${nodeId}`, error);

      return {
        success: false,
        nodeId,
        workflowId,
        executionTime,
        error: {
          message: error.message,
          code: error.code || 'STANDALONE_NODE_EXECUTION_ERROR',
          details: error.stack,
        },
        metadata: {
          nodeType: 'unknown',
          nodeName: nodeId,
          executedAt: Date.now(),
          hasContext: Object.keys(context).length > 0,
          contextKeys: Object.keys(context),
        },
      };
    }
  }

  /**
   * Load node information từ database
   */
  private async loadNodeInformation(workflowId: string, nodeId: string): Promise<{ nodeType: string; node: Node; }> {

    // Load node entity
    const node = await this.nodeRepository.findById(nodeId, workflowId);

    if (!node) {
      throw new NotFoundException(`Node not found: ${nodeId}`);
    }

    // Validate node belongs to workflow
    if (node.workflowId !== workflowId) {
      throw new Error(`Node ${nodeId} does not belong to workflow ${workflowId}`);
    }

    // Load node definition
    const nodeType = await this.nodeDefinitionRepository.findTypeById(node.nodeDefinitionId)

    if (!nodeType) {
      throw new Error(`Node definition not found for node: ${nodeId}`);
    }

    return {
      nodeType,
      node,
    };
  }

  /**
   * Execute node logic thông qua executor system
   */
  private async executeNodeLogic(
    nodeType: string,
    node: Node,
    context: WorkflowContext,
  ): Promise<Record<string, any>> {

    switch (NodeTypeUtils.stringToNodeType(nodeType)) {
      case ENodeType.HTTP_REQUEST:
        return await this.executeHttpRequest(node, context);

      case ENodeType.IF_CONDITION:
        return await this.executeIfCondition(node, context);

      case ENodeType.SWITCH:
        return await this.executeSwitch(node, context);

      case ENodeType.LOOP:
        return await this.executeLoop(node, context);

      case ENodeType.EDIT_FIELDS:
        return await this.executeEditFields(node, context);

      case ENodeType.MERGE:
        return await this.executeMerge(node, context);

      case ENodeType.FILTER:
        return await this.executeFilter(node, context);

      case ENodeType.WAIT:
        return await this.executeWait(node, context);

      default:
        throw new Error(`Unsupported node type: ${nodeType}`);
    }
  }

  // ===== HTTP NODE IMPLEMENTATIONS =====

  /**
   * Execute HTTP Request node
   */
  private async executeHttpRequest(
    node: Node,
    context: WorkflowContext,
  ): Promise<IHttpRequestOutput> {
    this.logger.log(`Executing HTTP Request node: ${node.id}`);

    try {
      const parameters = node.parameters as IHttpRequestParameters;

      // Resolve template variables in parameters
      const resolvedParameters: IHttpRequestParameters = {
        ...parameters,
        url: this.resolveTemplateVariables(parameters.url, context),
        headers: this.resolveTemplateVariables(parameters.headers, context),
        query_params: this.resolveTemplateVariables(parameters.query_params, context),
        body: this.resolveTemplateVariables(parameters.body, context)
      };

      this.logger.debug(`HTTP Request details: ${resolvedParameters.method} ${resolvedParameters.url}`);

      // Execute HTTP request using HttpRequestExecutor
      const result: IHttpRequestOutput = await this.httpRequestExecutor.execute(resolvedParameters);

      // Return standardized result format with detailed code and response
      return result;

    } catch (error) {
      return {
        status: EStatusNode.ERROR,
        data: {
          code: HttpStatusCode.InternalServerError,
          response: {
            message: error.message || 'HTTP request failed',
          }
        }
      };
    }
  }

  /**
   * Execute If Condition node
   */
  private async executeIfCondition(
    node: Node, context: WorkflowContext
  ): Promise<Record<string, any>> {
    this.logger.log(`Executing If Condition node: ${node.id}`);

    try {
      const parameters = node.parameters as any;
      const { condition, trueValue, falseValue } = parameters;

      // Simple condition evaluation (in real implementation, use proper expression evaluator)
      const conditionResult = this.evaluateCondition(condition, context);

      const result = {
        success: true,
        condition_met: conditionResult,
        condition_expression: condition,
        result_value: conditionResult ? trueValue : falseValue,
        evaluated_at: new Date().toISOString(),
        metadata: {
          nodeId: node.id,
          condition,
          evaluation_result: conditionResult,
        }
      };

      this.logger.log(`If Condition evaluated: ${node.id}, result: ${conditionResult}`);
      return result;

    } catch (error) {
      this.logger.error(`If Condition execution failed: ${node.id}`, error);
      throw error;
    }
  }

  /**
   * Execute Switch node
   */
  private async executeSwitch(node: Node, context: Record<string, any>): Promise<Record<string, any>> {
    this.logger.log(`Executing Switch node: ${node.id}`);

    try {
      const parameters = node.parameters as any;
      const { switchExpression, cases = [], defaultCase } = parameters;

      // Evaluate switch expression
      const expressionValue = this.evaluateExpression(switchExpression, context);

      // Find matching case
      let matchedCase: any = null;
      for (const caseItem of cases) {
        if (caseItem.value === expressionValue) {
          matchedCase = caseItem;
          break;
        }
      }

      const result = {
        success: true,
        switch_expression: switchExpression,
        expression_value: expressionValue,
        matched_case: matchedCase,
        result_value: matchedCase ? matchedCase.result : defaultCase,
        evaluated_at: new Date().toISOString(),
        metadata: {
          nodeId: node.id,
          total_cases: cases.length,
          has_default: !!defaultCase,
        }
      };

      this.logger.log(`Switch evaluated: ${node.id}, matched: ${!!matchedCase}`);
      return result;

    } catch (error) {
      this.logger.error(`Switch execution failed: ${node.id}`, error);
      throw error;
    }
  }

  /**
   * Execute Loop node
   */
  private async executeLoop(node: Node, context: Record<string, any>): Promise<Record<string, any>> {
    this.logger.log(`Executing Loop node: ${node.id}`);

    try {
      const parameters = node.parameters as any;
      const { loopType = 'for', iterations = 1, condition, items = [] } = parameters;

      const results: any[] = [];
      let loopCount = 0;

      if (loopType === 'for') {
        // For loop with specified iterations
        for (let i = 0; i < iterations; i++) {
          const iterationResult = {
            iteration: i + 1,
            data: `Loop iteration ${i + 1}`,
            timestamp: Date.now(),
          };
          results.push(iterationResult);
          loopCount++;
        }
      } else if (loopType === 'foreach') {
        // For each loop over items
        for (let i = 0; i < items.length; i++) {
          const iterationResult = {
            iteration: i + 1,
            item: items[i],
            data: `Processing item: ${JSON.stringify(items[i])}`,
            timestamp: Date.now(),
          };
          results.push(iterationResult);
          loopCount++;
        }
      }

      const result = {
        success: true,
        loop_type: loopType,
        total_iterations: loopCount,
        results,
        completed_at: new Date().toISOString(),
        metadata: {
          nodeId: node.id,
          loop_type: loopType,
          planned_iterations: iterations,
          actual_iterations: loopCount,
        }
      };

      this.logger.log(`Loop completed: ${node.id}, iterations: ${loopCount}`);
      return result;

    } catch (error) {
      this.logger.error(`Loop execution failed: ${node.id}`, error);
      throw error;
    }
  }

  /**
   * Execute Edit Fields node
   */
  private async executeEditFields(node: Node, context: Record<string, any>): Promise<Record<string, any>> {
    this.logger.log(`Executing Edit Fields node: ${node.id}`);

    try {
      const parameters = node.parameters as any;
      const { operations = [] } = parameters;

      // Get input data from context
      const inputData = context.inputData || context.previousOutputs || {};
      const editedData = { ...inputData };

      const appliedOperations = [];

      // Apply field operations
      for (const operation of operations) {
        const { type, field, value, newField } = operation;

        switch (type) {
          case 'set':
            editedData[field] = value;
            appliedOperations.push({ type: 'set', field, value });
            break;
          case 'remove':
            delete editedData[field];
            appliedOperations.push({ type: 'remove', field });
            break;
          case 'rename':
            if (editedData[field] !== undefined) {
              editedData[newField] = editedData[field];
              delete editedData[field];
              appliedOperations.push({ type: 'rename', from: field, to: newField });
            }
            break;
        }
      }

      const result = {
        success: true,
        original_data: inputData,
        edited_data: editedData,
        operations_applied: appliedOperations,
        fields_count: Object.keys(editedData).length,
        processed_at: new Date().toISOString(),
        metadata: {
          nodeId: node.id,
          total_operations: operations.length,
          applied_operations: appliedOperations.length,
        }
      };

      this.logger.log(`Edit Fields completed: ${node.id}, operations: ${appliedOperations.length}`);
      return result;

    } catch (error) {
      this.logger.error(`Edit Fields execution failed: ${node.id}`, error);
      throw error;
    }
  }

  /**
   * Execute Merge node
   */
  private async executeMerge(node: Node, context: Record<string, any>): Promise<Record<string, any>> {
    this.logger.log(`Executing Merge node: ${node.id}`);

    try {
      const parameters = node.parameters as any;
      const { mergeMode = 'combine', sources = [] } = parameters;

      // Get data from different sources
      const inputData = context.inputData || {};
      const previousOutputs = context.previousOutputs || {};
      const triggerData = context.triggerData || {};

      let mergedData = {};

      switch (mergeMode) {
        case 'combine':
          // Combine all data sources
          mergedData = {
            ...triggerData,
            ...previousOutputs,
            ...inputData,
          };
          break;
        case 'array':
          // Merge as array
          mergedData = {
            merged_array: [triggerData, previousOutputs, inputData].filter(item =>
              item && Object.keys(item).length > 0
            ),
          };
          break;
        case 'selective':
          // Merge only specified sources
          for (const source of sources) {
            if (source === 'input' && inputData) {
              mergedData = { ...mergedData, ...inputData };
            } else if (source === 'previous' && previousOutputs) {
              mergedData = { ...mergedData, ...previousOutputs };
            } else if (source === 'trigger' && triggerData) {
              mergedData = { ...mergedData, ...triggerData };
            }
          }
          break;
      }

      const result = {
        success: true,
        merge_mode: mergeMode,
        merged_data: mergedData,
        source_count: sources.length || 3,
        merged_fields: Object.keys(mergedData).length,
        processed_at: new Date().toISOString(),
        metadata: {
          nodeId: node.id,
          merge_mode: mergeMode,
          sources_used: sources,
        }
      };

      this.logger.log(`Merge completed: ${node.id}, fields: ${Object.keys(mergedData).length}`);
      return result;

    } catch (error) {
      this.logger.error(`Merge execution failed: ${node.id}`, error);
      throw error;
    }
  }

  /**
   * Execute Filter node
   */
  private async executeFilter(node: Node, context: Record<string, any>): Promise<Record<string, any>> {
    this.logger.log(`Executing Filter node: ${node.id}`);

    try {
      const parameters = node.parameters as any;
      const { filterCondition, filterType = 'include' } = parameters;

      // Get input data
      const inputData = context.inputData || context.previousOutputs || {};
      const dataArray = Array.isArray(inputData) ? inputData : [inputData];

      const filteredData = dataArray.filter(item => {
        // Simple filter evaluation (in real implementation, use proper expression evaluator)
        return this.evaluateFilterCondition(filterCondition, item, context);
      });

      const result = {
        success: true,
        filter_condition: filterCondition,
        filter_type: filterType,
        original_count: dataArray.length,
        filtered_count: filteredData.length,
        filtered_data: filteredData,
        processed_at: new Date().toISOString(),
        metadata: {
          nodeId: node.id,
          filter_condition: filterCondition,
          items_removed: dataArray.length - filteredData.length,
        }
      };

      this.logger.log(`Filter completed: ${node.id}, ${filteredData.length}/${dataArray.length} items passed`);
      return result;

    } catch (error) {
      this.logger.error(`Filter execution failed: ${node.id}`, error);
      throw error;
    }
  }

  /**
   * Execute Wait node
   */
  private async executeWait(node: Node, context: Record<string, any>): Promise<Record<string, any>> {
    this.logger.log(`Executing Wait node: ${node.id}`);

    try {
      const parameters = node.parameters as any;
      const { waitType = 'duration', duration = 1000, waitUntil } = parameters;

      const startTime = Date.now();

      if (waitType === 'duration') {
        // Wait for specified duration
        await new Promise(resolve => setTimeout(resolve, duration));
      } else if (waitType === 'until') {
        // Wait until condition is met (simplified implementation)
        let conditionMet = false;
        const maxWait = 30000; // 30 seconds max
        const checkInterval = 1000; // Check every second

        while (!conditionMet && (Date.now() - startTime) < maxWait) {
          conditionMet = this.evaluateCondition(waitUntil, context);
          if (!conditionMet) {
            await new Promise(resolve => setTimeout(resolve, checkInterval));
          }
        }
      }

      const actualWaitTime = Date.now() - startTime;

      const result = {
        success: true,
        wait_type: waitType,
        planned_duration: duration,
        actual_duration: actualWaitTime,
        wait_condition: waitUntil,
        completed_at: new Date().toISOString(),
        metadata: {
          nodeId: node.id,
          wait_type: waitType,
          duration_ms: actualWaitTime,
        }
      };

      this.logger.log(`Wait completed: ${node.id}, duration: ${actualWaitTime}ms`);
      return result;

    } catch (error) {
      this.logger.error(`Wait execution failed: ${node.id}`, error);
      throw error;
    }
  }

  // ===== HELPER METHODS =====

  /**
   * Evaluate condition expression (simplified implementation)
   */
  private evaluateCondition(condition: string, context: Record<string, any>): boolean {
    if (!condition) return true;

    try {
      // Simple condition evaluation - in production, use proper expression evaluator
      // This is a basic implementation for demonstration

      // Handle simple comparisons like "value > 10", "status == 'active'"
      const operators = ['>=', '<=', '==', '!=', '>', '<'];

      for (const operator of operators) {
        if (condition.includes(operator)) {
          const [left, right] = condition.split(operator).map(s => s.trim());

          // Get left value from context
          const leftValue = this.getValueFromContext(left, context);

          // Parse right value
          const rightValue = this.parseValue(right);

          // Evaluate comparison
          switch (operator) {
            case '>': return leftValue > rightValue;
            case '<': return leftValue < rightValue;
            case '>=': return leftValue >= rightValue;
            case '<=': return leftValue <= rightValue;
            case '==': return leftValue == rightValue;
            case '!=': return leftValue != rightValue;
          }
        }
      }

      // If no operator found, treat as boolean
      return this.getValueFromContext(condition, context);

    } catch (error) {
      this.logger.warn(`Condition evaluation failed: ${condition}`, error);
      return false;
    }
  }

  /**
   * Evaluate expression (simplified implementation)
   */
  private evaluateExpression(expression: string, context: Record<string, any>): any {
    if (!expression) return null;

    try {
      // Simple expression evaluation
      return this.getValueFromContext(expression, context);
    } catch (error) {
      this.logger.warn(`Expression evaluation failed: ${expression}`, error);
      return null;
    }
  }

  /**
   * Evaluate filter condition
   */
  private evaluateFilterCondition(condition: string, item: any, context: Record<string, any>): boolean {
    if (!condition) return true;

    try {
      // Create context with current item
      const filterContext = { ...context, item, current: item };
      return this.evaluateCondition(condition, filterContext);
    } catch (error) {
      this.logger.warn(`Filter condition evaluation failed: ${condition}`, error);
      return false;
    }
  }

  /**
   * Get value from context using dot notation
   */
  private getValueFromContext(path: string, context: Record<string, any>): any {
    if (!path || !context) return null;

    try {
      // Handle simple paths like "data.field" or "previousOutputs.nodeId.result"
      const keys = path.split('.');
      let value = context;

      for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
          value = value[key];
        } else {
          return null;
        }
      }

      return value;
    } catch (error) {
      return null;
    }
  }

  /**
   * Parse value from string (handle numbers, booleans, strings)
   */
  private parseValue(value: string): any {
    if (!value) return null;

    const trimmed = value.trim();

    // Handle quoted strings
    if ((trimmed.startsWith('"') && trimmed.endsWith('"')) ||
      (trimmed.startsWith("'") && trimmed.endsWith("'"))) {
      return trimmed.slice(1, -1);
    }

    // Handle booleans
    if (trimmed === 'true') return true;
    if (trimmed === 'false') return false;
    if (trimmed === 'null') return null;

    // Handle numbers
    const num = Number(trimmed);
    if (!isNaN(num)) return num;

    // Return as string
    return trimmed;
  }

  /**
   * Helper method to resolve template variables using WorkflowTemplateProcessorService
   */
  private resolveTemplateVariables(value: any, workflowContext: WorkflowContext): any {
    if (!value) return value;

    if (typeof value === 'string') {
      return this.templateProcessor.processTemplate(value, workflowContext);
    }

    if (typeof value === 'object' && value !== null) {
      return this.templateProcessor.processObjectTemplate(value, workflowContext);
    }

    return value;
  }
}