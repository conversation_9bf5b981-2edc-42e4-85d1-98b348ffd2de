/**
 * ✅ SIMPLIFIED WORKFLOW CONTEXT INTERFACES
 * Core interfaces cho Simplified Workflow Context system
 */

/**
 * Metadata về workflow execution
 */
export interface WorkflowExecutionMetadata {
  /** UUID của execution instance */
  executionId: string;
  
  /** UUID của workflow definition */
  workflowId: string;
  
  /** ID của user thực thi workflow */
  userId: number;
  
  /** Timestamp bắt đầu execution (milliseconds) */
  startTime: number;
  
  /** Tổng số nodes trong workflow */
  totalNodes: number;
  
  /** Số nodes đã hoàn thành thành công */
  completedNodes: number;
  
  /** Số nodes thất bại */
  failedNodes: number;
  
  /** Timestamp kết thúc execution (optional) */
  endTime?: number;
  
  /** Tổng thời gian execution (milliseconds) */
  executionDuration?: number;
  
  /** Trạng thái hiện tại của execution */
  status?: 'running' | 'completed' | 'failed' | 'cancelled';
}

/**
 * Simplified Workflow Context - Interface chính
 */
export interface WorkflowContext {
  /**
   * JSON namespace chứa outputs của tất cả nodes
   * Structure: { node_name: { field1: value1, field2: value2, ... } }
   * 
   * Usage trong templates:
   * - {{json.webhook_trigger.user_id}}
   * - {{json.http_request.response.data}}
   * - {{json.database_query.results[0].name}}
   */
  json: Record<string, Record<string, any>>;
  
  /**
   * Metadata về workflow execution
   * Chứa thông tin tracking và progress
   * 
   * Usage trong templates:
   * - {{metadata.executionId}}
   * - {{metadata.completedNodes}}/{{metadata.totalNodes}}
   */
  metadata: WorkflowExecutionMetadata;
}

/**
 * Type helpers
 */
export type NodeOutputData = Record<string, any>;
export type JsonNamespace = Record<string, NodeOutputData>;

/**
 * Options cho các helper functions
 */
export interface JsonContextOptions {
  overwrite?: boolean;
  validateData?: boolean;
  sanitizeNodeName?: boolean;
  defaultValue?: any;
  throwOnNotFound?: boolean;
  caseSensitive?: boolean;
  skipInvalid?: boolean;
  includeMetadata?: boolean;
  maxDepth?: number;
  includeArrayIndices?: boolean;
  prefix?: string;
}

/**
 * Result types
 */
export interface AddNodeResult {
  nodeName: string;
  success: boolean;
  error?: string;
}

export interface GetValueResult {
  values: Record<string, any>;
  metadata: Record<string, { success: boolean; error?: string }>;
}
